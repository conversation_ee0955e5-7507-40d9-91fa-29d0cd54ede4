// Centralized message keys for API responses
// These keys should be used instead of hardcoded strings in API responses

export const MESSAGE_KEYS = {
  // Success messages
  SUCCESS: {
    CREATED: "api.success.created",
    UPDATED: "api.success.updated", 
    DELETED: "api.success.deleted",
    FETCHED: "api.success.fetched",
    LOGGED_IN: "api.success.logged_in",
    LOGGED_OUT: "api.success.logged_out",
    PASSWORD_CHANGED: "api.success.password_changed",
    PASSWORD_RESET_SENT: "api.success.password_reset_sent",
    EMAIL_VERIFIED: "api.success.email_verified",
    VERIFICATION_SENT: "api.success.verification_sent",
    BULK_IMPORT_COMPLETED: "api.success.bulk_import_completed",
    BULK_UPDATE_COMPLETED: "api.success.bulk_update_completed",
    BULK_DELETE_COMPLETED: "api.success.bulk_delete_completed"
  },

  // Error messages
  ERROR: {
    // Validation errors
    VALIDATION_FAILED: "api.error.validation_failed",
    INVALID_EMAIL: "api.error.invalid_email",
    INVALID_PHONE: "api.error.invalid_phone",
    INVALID_ID: "api.error.invalid_id",
    INVALID_TOKEN: "api.error.invalid_token",
    INVALID_CREDENTIALS: "api.error.invalid_credentials",
    INVALID_UPDATE_DATA: "api.error.invalid_update_data",
    
    // Resource errors
    NOT_FOUND: "api.error.not_found",
    DUPLICATE_RESOURCE: "api.error.duplicate_resource",
    DUPLICATE_EMAIL: "api.error.duplicate_email",
    DUPLICATE_PHONE: "api.error.duplicate_phone",
    DUPLICATE_NAME: "api.error.duplicate_name",
    
    // Operation errors
    CREATE_FAILED: "api.error.create_failed",
    UPDATE_FAILED: "api.error.update_failed",
    DELETE_FAILED: "api.error.delete_failed",
    FETCH_FAILED: "api.error.fetch_failed",
    LOGIN_FAILED: "api.error.login_failed",
    LOGOUT_FAILED: "api.error.logout_failed",
    
    // Server errors
    INTERNAL_SERVER_ERROR: "api.error.internal_server_error",
    DATABASE_ERROR: "api.error.database_error",
    NETWORK_ERROR: "api.error.network_error",
    
    // Authentication errors
    UNAUTHORIZED: "api.error.unauthorized",
    FORBIDDEN: "api.error.forbidden",
    SESSION_EXPIRED: "api.error.session_expired",
    SESSION_REQUIRED: "api.error.session_required",
    
    // Bulk operation errors
    BULK_IMPORT_FAILED: "api.error.bulk_import_failed",
    BULK_UPDATE_FAILED: "api.error.bulk_update_failed",
    BULK_DELETE_FAILED: "api.error.bulk_delete_failed",
    INVALID_DATA_FORMAT: "api.error.invalid_data_format",
    NO_DATA_PROVIDED: "api.error.no_data_provided",
    
    // Search and filter errors
    SEARCH_KEYWORD_EMPTY: "api.error.search_keyword_empty",
    FILTER_FIELD_EMPTY: "api.error.filter_field_empty",
    SORT_FIELD_EMPTY: "api.error.sort_field_empty",
    INVALID_SORT_DIRECTION: "api.error.invalid_sort_direction",
    
    // File operation errors
    FILE_NOT_FOUND: "api.error.file_not_found",
    FILE_UPLOAD_FAILED: "api.error.file_upload_failed",
    INVALID_FILE_FORMAT: "api.error.invalid_file_format",
    FILE_TOO_LARGE: "api.error.file_too_large"
  },

  // Entity-specific messages
  CONTACT: {
    CREATED: "api.contact.created",
    UPDATED: "api.contact.updated",
    DELETED: "api.contact.deleted",
    NOT_FOUND: "api.contact.not_found",
    DUPLICATE_PHONE: "api.contact.duplicate_phone",
    DUPLICATE_NAME: "api.contact.duplicate_name",
    CREATE_FAILED: "api.contact.create_failed",
    UPDATE_FAILED: "api.contact.update_failed",
    DELETE_FAILED: "api.contact.delete_failed",
    FETCH_FAILED: "api.contact.fetch_failed"
  },

  MESSAGE_TEMPLATE: {
    CREATED: "api.message_template.created",
    UPDATED: "api.message_template.updated", 
    DELETED: "api.message_template.deleted",
    NOT_FOUND: "api.message_template.not_found",
    DUPLICATE_NAME: "api.message_template.duplicate_name",
    CREATE_FAILED: "api.message_template.create_failed",
    UPDATE_FAILED: "api.message_template.update_failed",
    DELETE_FAILED: "api.message_template.delete_failed",
    FETCH_FAILED: "api.message_template.fetch_failed"
  },

  AI_RULE: {
    CREATED: "api.ai_rule.created",
    UPDATED: "api.ai_rule.updated",
    DELETED: "api.ai_rule.deleted", 
    NOT_FOUND: "api.ai_rule.not_found",
    DUPLICATE_NAME: "api.ai_rule.duplicate_name",
    CREATE_FAILED: "api.ai_rule.create_failed",
    UPDATE_FAILED: "api.ai_rule.update_failed",
    DELETE_FAILED: "api.ai_rule.delete_failed",
    FETCH_FAILED: "api.ai_rule.fetch_failed"
  },

  USER: {
    CREATED: "api.user.created",
    UPDATED: "api.user.updated",
    DELETED: "api.user.deleted",
    NOT_FOUND: "api.user.not_found",
    DUPLICATE_EMAIL: "api.user.duplicate_email",
    CREATE_FAILED: "api.user.create_failed",
    UPDATE_FAILED: "api.user.update_failed", 
    DELETE_FAILED: "api.user.delete_failed",
    FETCH_FAILED: "api.user.fetch_failed"
  },

  // Search and filter specific messages
  SEARCH: {
    INVALID_QUERY: "api.search.invalid_query",
    NO_RESULTS: "api.search.no_results",
    QUERY_TOO_SHORT: "api.search.query_too_short",
    QUERY_TOO_LONG: "api.search.query_too_long"
  },

  FILTER: {
    INVALID_VALUE: "api.filter.invalid_value",
    UNSUPPORTED_FIELD: "api.filter.unsupported_field",
    INVALID_OPERATOR: "api.filter.invalid_operator"
  }
} as const;

// Helper function to get nested message keys
export function getMessageKey(path: string): string {
  const keys = path.split('.');
  let current: any = MESSAGE_KEYS;
  
  for (const key of keys) {
    current = current[key];
    if (!current) {
      console.warn(`Message key not found: ${path}`);
      return path; // Return the path as fallback
    }
  }
  
  return current;
}

// Type-safe message key access
export type MessageKeyPath = 
  | `SUCCESS.${keyof typeof MESSAGE_KEYS.SUCCESS}`
  | `ERROR.${keyof typeof MESSAGE_KEYS.ERROR}`
  | `CONTACT.${keyof typeof MESSAGE_KEYS.CONTACT}`
  | `MESSAGE_TEMPLATE.${keyof typeof MESSAGE_KEYS.MESSAGE_TEMPLATE}`
  | `AI_RULE.${keyof typeof MESSAGE_KEYS.AI_RULE}`
  | `USER.${keyof typeof MESSAGE_KEYS.USER}`
  | `SEARCH.${keyof typeof MESSAGE_KEYS.SEARCH}`
  | `FILTER.${keyof typeof MESSAGE_KEYS.FILTER}`;

export function getTypedMessageKey(path: MessageKeyPath): string {
  return getMessageKey(path);
}
