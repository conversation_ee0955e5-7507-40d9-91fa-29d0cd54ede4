import { ContactBusinessLogicInterface, Contact } from "@/lib/repositories/contacts/interface";
import { ResponseWrapper } from "@/lib/types/responseWrapper";
import { ContactCreateSchema, ContactUpdateSchema } from "@/lib/validations/contact";
import { ERROR_CODES } from "@/app/api/error_codes";
import { GetAllResultPaginated, ContactQueryParams } from "./types";

// Create Contact Implementation
export async function implHandleCreateContact(
  data: any,
  businessLogic: ContactBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate input data
    const validationResult = ContactCreateSchema.safeParse(data);
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err =>
        `${err.path.join('.')}: ${err.message}`
      );

      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          errors,
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // Transform notes to include createdAt if not present
    const createData = {
      ...validationResult.data,
      notes: validationResult.data.notes?.map(note => ({
        text: note.text,
        createdAt: new Date().toISOString()
      }))
    };

    const contacts = await businessLogic.create(createData);

    return {
      status: 201,
      body: new ResponseWrapper("success", contacts),
    };
  } catch (error: any) {
    console.error("Create contacts error:", error);

    if (error.code === "DUPLICATE_PHONE" || error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE]
        ),
      };
    }

    if (error.code === "INVALID_EMAIL") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to create contacts. Please try again."],
        [ERROR_CODES.CREATE_FAILED]
      ),
    };
  }
}

// Update Contact Implementation
export async function implHandleUpdateContact(
  id: string,
  data: any,
  businessLogic: ContactBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate input data
    const validationResult = ContactUpdateSchema.safeParse(data);
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err =>
        `${err.path.join('.')}: ${err.message}`
      );

      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          errors,
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    const contacts = await businessLogic.update(id, validationResult.data);

    if (!contacts) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Contact not found"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", contacts),
    };
  } catch (error: any) {
    console.error("Update contacts error:", error);

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    if (error.code === "DUPLICATE_PHONE" || error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE]
        ),
      };
    }

    if (error.code === "INVALID_UPDATE_DATA") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    if (error.code === "INVALID_EMAIL" || error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to update contacts. Please try again."],
        [ERROR_CODES.UPDATE_FAILED]
      ),
    };
  }
}

// Get Contact by ID Implementation
export async function implHandleGetContact(
  id: string,
  businessLogic: ContactBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    const contacts = await businessLogic.getById(id);

    if (!contacts) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Contact not found"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", contacts),
    };
  } catch (error: any) {
    console.error("Get contacts error:", error);

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to fetch contacts. Please try again."],
        [ERROR_CODES.FETCH_FAILED]
      ),
    };
  }
}

// Get All Contacts Implementation (handles all, search, and tag filtering)
export async function implHandleGetAllContacts(
  businessLogic: ContactBusinessLogicInterface,
  params?: ContactQueryParams
): Promise<{
  status: number;
  body: ResponseWrapper<GetAllResultPaginated<Contact>>;
}> {
  try {
    // Validate search parameter if provided
    if (params?.search !== undefined) {
      if (!params.search || params.search.trim() === '') {
        return {
          status: 400,
          body: new ResponseWrapper<GetAllResultPaginated<Contact>>(
            "failed",
            undefined,
            ["Search keyword cannot be empty"],
            [ERROR_CODES.VALIDATION_FAILED]
          ),
        };
      }
    }

    // Validate filters if provided
    if (params?.filters && params.filters.length > 0) {
      for (const filter of params.filters) {
        if (!filter.field || filter.field.trim() === '') {
          return {
            status: 400,
            body: new ResponseWrapper<GetAllResultPaginated<Contact>>(
              "failed",
              undefined,
              ["Filter field cannot be empty"],
              [ERROR_CODES.VALIDATION_FAILED]
            ),
          };
        }
      }
    }

    // Validate sorts if provided
    if (params?.sorts && params.sorts.length > 0) {
      for (const sort of params.sorts) {
        if (!sort.field || sort.field.trim() === '') {
          return {
            status: 400,
            body: new ResponseWrapper<GetAllResultPaginated<Contact>>(
              "failed",
              undefined,
              ["Sort field cannot be empty"],
              [ERROR_CODES.VALIDATION_FAILED]
            ),
          };
        }
        if (!['asc', 'desc'].includes(sort.direction)) {
          return {
            status: 400,
            body: new ResponseWrapper<GetAllResultPaginated<Contact>>(
              "failed",
              undefined,
              ["Sort direction must be 'asc' or 'desc'"],
              [ERROR_CODES.VALIDATION_FAILED]
            ),
          };
        }
      }
    }

    // Build query parameters for the repository
    const queryParams: any = {
      includeDeleted: params?.includeDeleted,
      page: params?.page,
      limit: params?.limit,
    };

    // Add search if provided
    if (params?.search) {
      queryParams.search = params.search.trim();
    }

    // Add filters if provided
    if (params?.filters && params.filters.length > 0) {
      queryParams.filters = params.filters;
    }

    // Add sorts if provided
    if (params?.sorts && params.sorts.length > 0) {
      queryParams.sorts = params.sorts;
    }

    const result = await businessLogic.getAll(queryParams);

    return {
      status: 200,
      body: new ResponseWrapper<GetAllResultPaginated<Contact>>("success", { ...result, page: queryParams.page }),
    };
  } catch (error: any) {
    console.error("Get contacts error:", error);

    return {
      status: 500,
      body: new ResponseWrapper<GetAllResultPaginated<Contact>>(
        "failed",
        undefined,
        ["Failed to fetch contacts. Please try again."],
        [ERROR_CODES.FETCH_FAILED]
      ),
    };
  }
}

// Delete Contact Implementation
export async function implHandleDeleteContact(
  id: string,
  businessLogic: ContactBusinessLogicInterface,
  hardDelete: boolean = false
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    await businessLogic.delete(id, hardDelete);

    return {
      status: 200,
      body: new ResponseWrapper("success", { message: "Contact deleted successfully" }),
    };
  } catch (error: any) {
    console.error("Delete contacts error:", error);

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to delete contacts. Please try again."],
        [ERROR_CODES.DELETE_FAILED]
      ),
    };
  }
}
