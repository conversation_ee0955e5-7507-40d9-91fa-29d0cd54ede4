import { Contact } from "@/lib/repositories/contacts/interface";

// Common types used across contact implementations
export interface GetAllResultPaginated<T> {
  items: T[];
  page: number;
  total: number;
}

// ============================================================================
// BULK OPERATIONS TYPES
// ============================================================================

export interface BulkImportRequest {
  operation: 'import';
  data: Array<{
    name: string;
    phone: string;
    email?: string;
    tags?: string[];
    notes?: Array<{ text: string; createdAt: string }>;
  }>;
}

export interface BulkUpdateRequest {
  operation: 'update';
  data: Array<{
    id: string;
    name: string;
    phone: string;
    email?: string;
    tags?: string[];
    notes?: Array<{ text: string; createdAt: string }>;
  }>;
}

export interface BulkDeleteRequest {
  operation: 'delete';
  ids: string[];
}

export interface BulkOperationResult {
  total: number;
  successful: number;
  failed: number;
  errors: Array<{
    row?: number;
    id?: string;
    field?: string;
    message: string;
  }>;
}

// ============================================================================
// STATS OPERATIONS TYPES
// ============================================================================

export interface ContactsStatsData {
  totalContacts: number;
  activeContacts: number;
  deletedContacts: number;
  contactsWithEmail: number;
  contactsWithTags: number;
  recentContacts: number; // last 7 days
  statusBreakdown: Array<{
    status: string;
    count: number;
    percentage: number;
  }>;
  tagBreakdown: Array<{
    tag: string;
    count: number;
    percentage: number;
  }>;
  createdByBreakdown: Array<{
    createdBy: string;
    count: number;
    percentage: number;
  }>;
  dailyStats: Array<{
    date: string;
    created: number;
    deleted: number;
  }>;
}

// ============================================================================
// QUERY PARAMETERS TYPES
// ============================================================================

export interface ContactQueryParams {
  search?: string;
  includeDeleted?: boolean;
  page?: number;
  limit?: number;
  sorts?: {
    field: keyof Contact | string;
    direction: "asc" | "desc";
  }[];
  filters?: {
    field: keyof Contact | string;
    value: Contact[keyof Contact] | any;
  }[];
}

export interface ContactStatsParams {
  search?: string;
  includeDeleted?: boolean;
  filters?: {
    field: string;
    value: any;
  }[];
  dateFrom?: string;
  dateTo?: string;
}
