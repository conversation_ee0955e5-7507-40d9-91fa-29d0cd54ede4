import { ContactBusinessLogicInterface } from "@/lib/repositories/contacts/interface";
import { ResponseWrapper } from "@/lib/types/responseWrapper";
import { ContactCreateSchema } from "@/lib/validations/contact";
import { ERROR_CODES } from "@/app/api/error_codes";

// Create Contact Implementation
export async function implHandleCreateContact(
  data: any,
  businessLogic: ContactBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate input data
    const validationResult = ContactCreateSchema.safeParse(data);
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err =>
        `${err.path.join('.')}: ${err.message}`
      );

      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          errors,
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // Transform notes to include createdAt if not present
    const createData = {
      ...validationResult.data,
      notes: validationResult.data.notes?.map(note => ({
        text: note.text,
        createdAt: new Date().toISOString()
      }))
    };

    const contacts = await businessLogic.create(createData);

    return {
      status: 201,
      body: new ResponseWrapper("success", contacts),
    };
  } catch (error: any) {
    console.error("Create contacts error:", error);

    if (error.code === "DUPLICATE_PHONE" || error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE]
        ),
      };
    }

    if (error.code === "INVALID_EMAIL") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to create contacts. Please try again."],
        [ERROR_CODES.CREATE_FAILED]
      ),
    };
  }
}
