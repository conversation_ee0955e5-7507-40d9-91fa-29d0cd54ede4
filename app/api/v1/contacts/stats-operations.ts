import { ContactBusinessLogicInterface } from "@/lib/repositories/contacts/interface";
import { ResponseWrapper } from "@/lib/types/responseWrapper";
import { ERROR_CODES } from "@/app/api/error_codes";
import { ContactsStatsData, ContactStatsParams } from "./types";

// Get Contacts Stats Implementation
export async function implHandleGetContactsStats(
  businessLogic: ContactBusinessLogicInterface,
  params?: ContactStatsParams
): Promise<{
  status: number;
  body: ResponseWrapper<ContactsStatsData>;
}> {
  try {
    // Get all contacts with filters
    const allContactsResult = await businessLogic.getAll({
      search: params?.search,
      includeDeleted: params?.includeDeleted,
      filters: params?.filters,
      limit: 10000 // Get all for stats calculation
    });

    const contacts = allContactsResult.items;
    const totalContacts = contacts.length;

    // Calculate basic stats
    const activeContacts = contacts.filter(c => !c.deletedAt).length;
    const deletedContacts = contacts.filter(c => c.deletedAt).length;
    const contactsWithEmail = contacts.filter(c => c.email && c.email.trim() !== '').length;
    const contactsWithTags = contacts.filter(c => c.tags && c.tags.length > 0).length;

    // Recent contacts (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const recentContacts = contacts.filter(c => new Date(c.createdAt) >= sevenDaysAgo).length;

    // Status breakdown
    const statusBreakdown = [
      {
        status: 'Active',
        count: activeContacts,
        percentage: totalContacts > 0 ? Math.round((activeContacts / totalContacts) * 100) : 0
      },
      {
        status: 'Deleted',
        count: deletedContacts,
        percentage: totalContacts > 0 ? Math.round((deletedContacts / totalContacts) * 100) : 0
      }
    ];

    // Tag breakdown
    const tagCounts: Record<string, number> = {};
    contacts.forEach(contact => {
      if (contact.tags && contact.tags.length > 0) {
        contact.tags.forEach(tag => {
          tagCounts[tag] = (tagCounts[tag] || 0) + 1;
        });
      } else {
        tagCounts['No Tag'] = (tagCounts['No Tag'] || 0) + 1;
      }
    });

    const tagBreakdown = Object.entries(tagCounts)
      .map(([tag, count]) => ({
        tag,
        count,
        percentage: totalContacts > 0 ? Math.round((count / totalContacts) * 100) : 0
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10); // Top 10 tags

    // Created by breakdown
    const createdByCounts: Record<string, number> = {};
    contacts.forEach(contact => {
      const createdBy = contact.createdBy || 'System';
      createdByCounts[createdBy] = (createdByCounts[createdBy] || 0) + 1;
    });

    const createdByBreakdown = Object.entries(createdByCounts)
      .map(([createdBy, count]) => ({
        createdBy,
        count,
        percentage: totalContacts > 0 ? Math.round((count / totalContacts) * 100) : 0
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10); // Top 10 creators

    // Daily stats for the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const dailyStats: Array<{ date: string; created: number; deleted: number }> = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];

      const created = contacts.filter(c =>
        new Date(c.createdAt).toISOString().split('T')[0] === dateStr
      ).length;

      const deleted = contacts.filter(c =>
        c.deletedAt && new Date(c.deletedAt).toISOString().split('T')[0] === dateStr
      ).length;

      dailyStats.push({ date: dateStr, created, deleted });
    }

    const statsData: ContactsStatsData = {
      totalContacts,
      activeContacts,
      deletedContacts,
      contactsWithEmail,
      contactsWithTags,
      recentContacts,
      statusBreakdown,
      tagBreakdown,
      createdByBreakdown,
      dailyStats
    };

    return {
      status: 200,
      body: new ResponseWrapper("success", statsData),
    };
  } catch (error: any) {
    console.error("Get contacts stats error:", error);

    return {
      status: 500,
      body: new ResponseWrapper<ContactsStatsData>(
        "failed",
        undefined,
        ["Failed to fetch contacts statistics. Please try again."],
        [ERROR_CODES.FETCH_FAILED]
      ),
    };
  }
}
