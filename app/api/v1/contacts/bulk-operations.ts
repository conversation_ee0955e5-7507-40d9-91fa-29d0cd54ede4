import { ContactBusinessLogicInterface } from "@/lib/repositories/contacts/interface";
import { ResponseWrapper } from "@/lib/types/responseWrapper";
import { ContactCreateSchema } from "@/lib/validations/contact";
import { ERROR_CODES } from "@/app/api/error_codes";
import { 
  BulkImportRequest, 
  BulkUpdateRequest, 
  BulkDeleteRequest, 
  BulkOperationResult 
} from "./types";

// Bulk import implementation
export async function implBulkImportContacts(
  request: BulkImportRequest,
  businessLogic: ContactBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<BulkOperationResult>;
}> {
  try {
    const { data } = request;

    if (!Array.isArray(data)) {
      return {
        status: 400,
        body: new ResponseWrapper<BulkOperationResult>(
          "failed",
          undefined,
          ["Invalid data format - expected array"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    if (data.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper<BulkOperationResult>(
          "failed",
          undefined,
          ["No data provided for import"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // Validate all data first
    const validatedData: any[] = [];
    const validationErrors: Array<{ row: number; field: string; message: string }> = [];

    for (let i = 0; i < data.length; i++) {
      const contactData = data[i];

      // Validate using schema
      const validationResult = ContactCreateSchema.safeParse(contactData);
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(err =>
          `${err.path.join('.')}: ${err.message}`
        );
        validationErrors.push({
          row: i,
          field: 'validation',
          message: errors.join(', ')
        });
      } else {
        // Transform notes to include createdAt
        const createData = {
          ...validationResult.data,
          notes: validationResult.data.notes?.map(note => ({
            text: note.text,
            createdAt: new Date().toISOString()
          }))
        };
        validatedData.push(createData);
      }
    }

    const results: BulkOperationResult = {
      total: data.length,
      successful: 0,
      failed: validationErrors.length,
      errors: validationErrors
    };

    // If we have valid data, perform bulk create
    if (validatedData.length > 0) {
      try {
        const createdContacts = await businessLogic.bulkCreate(validatedData);
        results.successful = createdContacts.length;
      } catch (error: any) {
        // If bulk operation fails, mark all remaining as failed
        results.failed = data.length;
        results.successful = 0;
        results.errors = [{
          row: 0,
          field: 'general',
          message: error instanceof Error ? error.message : 'Bulk create operation failed'
        }];
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", results),
    };
  } catch (error: any) {
    console.error("Bulk import contacts error:", error);

    return {
      status: 500,
      body: new ResponseWrapper<BulkOperationResult>(
        "failed",
        undefined,
        ["Failed to import contacts. Please try again."],
        [ERROR_CODES.CREATE_FAILED]
      ),
    };
  }
}

// Bulk update implementation
export async function implBulkUpdateContacts(
  request: BulkUpdateRequest,
  businessLogic: ContactBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<BulkOperationResult>;
}> {
  try {
    const { data } = request;

    if (!Array.isArray(data)) {
      return {
        status: 400,
        body: new ResponseWrapper<BulkOperationResult>(
          "failed",
          undefined,
          ["Invalid data format - expected array"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    if (data.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper<BulkOperationResult>(
          "failed",
          undefined,
          ["No data provided for update"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // Validate all data and prepare updates
    const validatedUpdates: Array<{ id: string; data: any }> = [];
    const validationErrors: Array<{ row: number; field: string; message: string }> = [];

    for (let i = 0; i < data.length; i++) {
      const contactData = data[i];

      // Validate required fields
      if (!contactData.id) {
        validationErrors.push({
          row: i,
          field: 'id',
          message: 'ID is required for updates'
        });
        continue;
      }

      // Prepare update data (only include fields that exist in ContactUpdateInput)
      const updateData = {
        name: contactData.name,
        phone: contactData.phone,
        email: contactData.email,
        tags: contactData.tags,
        notes: contactData.notes?.map(note => ({
          text: note.text,
          createdAt: note.createdAt || new Date().toISOString()
        }))
      };

      validatedUpdates.push({
        id: contactData.id,
        data: updateData
      });
    }

    const results: BulkOperationResult = {
      total: data.length,
      successful: 0,
      failed: validationErrors.length,
      errors: validationErrors
    };

    // If we have valid updates, perform bulk update
    if (validatedUpdates.length > 0) {
      try {
        const updatedCount = await businessLogic.bulkUpdate(validatedUpdates);
        results.successful = updatedCount;

        // If some updates failed (updatedCount < validatedUpdates.length)
        if (updatedCount < validatedUpdates.length) {
          const failedCount = validatedUpdates.length - updatedCount;
          results.failed += failedCount;
          results.errors.push({
            row: 0,
            field: 'general',
            message: `${failedCount} contacts could not be updated (may not exist or have validation errors)`
          });
        }
      } catch (error: any) {
        // If bulk operation fails, mark all remaining as failed
        results.failed = data.length;
        results.successful = 0;
        results.errors = [{
          row: 0,
          field: 'general',
          message: error instanceof Error ? error.message : 'Bulk update operation failed'
        }];
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", results),
    };
  } catch (error: any) {
    console.error("Bulk update contacts error:", error);

    return {
      status: 500,
      body: new ResponseWrapper<BulkOperationResult>(
        "failed",
        undefined,
        ["Failed to update contacts. Please try again."],
        [ERROR_CODES.UPDATE_FAILED]
      ),
    };
  }
}

// Bulk delete implementation
export async function implBulkDeleteContacts(
  request: BulkDeleteRequest,
  businessLogic: ContactBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<BulkOperationResult>;
}> {
  try {
    const { ids } = request;

    if (!Array.isArray(ids)) {
      return {
        status: 400,
        body: new ResponseWrapper<BulkOperationResult>(
          "failed",
          undefined,
          ["Invalid IDs format - expected array"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    if (ids.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper<BulkOperationResult>(
          "failed",
          undefined,
          ["No IDs provided for deletion"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    const results: BulkOperationResult = {
      total: ids.length,
      successful: 0,
      failed: 0,
      errors: []
    };

    // Perform bulk delete operation
    try {
      const deletedCount = await businessLogic.bulkDelete(ids, false); // soft delete by default
      results.successful = deletedCount;

      // If some deletes failed (deletedCount < ids.length)
      if (deletedCount < ids.length) {
        const failedCount = ids.length - deletedCount;
        results.failed = failedCount;
        results.errors.push({
          id: 'bulk',
          message: `${failedCount} contacts could not be deleted (may not exist or already deleted)`
        });
      }
    } catch (error: any) {
      // If bulk operation fails, mark all as failed
      results.failed = ids.length;
      results.successful = 0;
      results.errors = [{
        id: 'bulk',
        message: error instanceof Error ? error.message : 'Bulk delete operation failed'
      }];
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", results),
    };
  } catch (error: any) {
    console.error("Bulk delete contacts error:", error);

    return {
      status: 500,
      body: new ResponseWrapper<BulkOperationResult>(
        "failed",
        undefined,
        ["Failed to delete contacts. Please try again."],
        [ERROR_CODES.DELETE_FAILED]
      ),
    };
  }
}

// Validation helpers
export function implValidateBulkImportRequest(body: any): body is BulkImportRequest {
  return (
    body &&
    body.operation === 'import' &&
    Array.isArray(body.data) &&
    body.data.length > 0 &&
    body.data.every((item: any) =>
      typeof item === 'object' &&
      typeof item.name === 'string' &&
      typeof item.phone === 'string'
    )
  );
}

export function implValidateBulkUpdateRequest(body: any): body is BulkUpdateRequest {
  return (
    body &&
    body.operation === 'update' &&
    Array.isArray(body.data) &&
    body.data.length > 0 &&
    body.data.every((item: any) =>
      typeof item === 'object' &&
      typeof item.id === 'string' &&
      typeof item.name === 'string' &&
      typeof item.phone === 'string'
    )
  );
}

export function implValidateBulkDeleteRequest(body: any): body is BulkDeleteRequest {
  return (
    body &&
    body.operation === 'delete' &&
    Array.isArray(body.ids) &&
    body.ids.length > 0 &&
    body.ids.every((id: any) => typeof id === 'string')
  );
}
