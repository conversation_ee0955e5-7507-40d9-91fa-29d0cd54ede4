import { NextRequest, NextResponse } from 'next/server'
import { implHandleBuildMongoQueries } from '../impl'
import { ERROR_CODES } from "@/app/api/error_codes";

// ✨ POST endpoint to build MongoDB queries from user parameters
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { entity, params } = body

    if (!entity) {
      return NextResponse.json(
        {
          status: "failed",
          data: null,
          errors: ["Entity parameter is required"],
          errorCodes: [ERROR_CODES.VALIDATION_FAILED]
        },
        { status: 400 }
      )
    }

    if (!params || typeof params !== 'object') {
      return NextResponse.json(
        {
          status: "failed",
          data: null,
          errors: ["Params object is required"],
          errorCodes: [ERROR_CODES.VALIDATION_FAILED]
        },
        { status: 400 }
      )
    }

    const result = await implHandleBuildMongoQueries(entity, params)
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Build queries POST route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    )
  }
}
