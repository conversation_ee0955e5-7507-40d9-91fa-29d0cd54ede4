import {
  StringSearch,
  ArraySearch,
  HasEmail,
  EmailSearch,
  SelectSearch,
  DateRangeSearch,
  SearchFilter,
  buildFiltersForApiResponse,
  buildMongoQueries
} from '../filters'

// ✨ Composable search configuration for contacts
export class ContactsSearchConfig {
  private filters: SearchFilter[]

  constructor() {
    this.filters = [
      // ✨ String-based searches
      StringSearch.contains('name', 'name', 'Name', {
        minLength: 2,
        placeholder: 'Search by contact name...'
      }),

      StringSearch.contains('phone', 'phone', 'Phone Number', {
        pattern: '^[+]?[0-9\\s\\-\\(\\)]+$',
        placeholder: 'Search by phone number...'
      }),

      // ✨ Email-specific searches
      EmailSearch.contains('email', 'email', 'Email Address', {
      }),

      HasEmail.basic('has_email', 'email', 'Has Email'),

      // ✨ Array-based searches
      ArraySearch.tags('tags', 'tags', 'Tags', [
        'customer', 'lead', 'partner', 'vendor', 'prospect',
        'vip', 'inactive', 'follow-up', 'qualified', 'unqualified'
      ]),

      // ✨ Select-based searches
      SelectSearch.status('status', 'status', 'Status', [
        'Active', 'Inactive', 'Pending', 'Archived', 'Deleted'
      ]),

      SelectSearch.custom('created_by', 'createdBy', 'Created By', [
        { value: 'system', label: 'System' },
        { value: 'admin', label: 'Admin' },
        { value: 'user', label: 'User' },
        { value: 'import', label: 'Import' },
        { value: 'api', label: 'API' }
      ]),

      // ✨ Date-based searches
      DateRangeSearch.createdAt('created_date', 'createdAt', 'Created Date', {
        allowSingleDate: true,
        defaultRange: 'today'
      }),

      DateRangeSearch.updatedAt('updated_date', 'updatedAt', 'Last Updated', {
        allowSingleDate: true
      }),

      // ✨ Boolean filters
      SelectSearch.boolean('has_phone', 'phone', 'Has Phone Number', 'Yes', 'No'),

      SelectSearch.boolean('has_notes', 'notes', 'Has Notes', 'Yes', 'No')
    ]
  }

  // ✨ Get all filters
  getFilters(): SearchFilter[] {
    return this.filters
  }

  // ✨ Build API response for frontend
  buildForApiResponse() {
    return {
      entity: 'contacts',
      filters: buildFiltersForApiResponse(this.filters),
      sortOptions: [
        { value: 'name', label: 'Name', field: 'name', type: 'string' },
        { value: 'email', label: 'Email', field: 'email', type: 'string' },
        { value: 'phone', label: 'Phone', field: 'phone', type: 'string' },
        { value: 'createdAt', label: 'Created Date', field: 'createdAt', type: 'date' },
        { value: 'updatedAt', label: 'Updated Date', field: 'updatedAt', type: 'date' },
        { value: 'status', label: 'Status', field: 'status', type: 'string' }
      ],
      dateFilterOptions: [
        { value: 'today', label: 'Today', description: 'Contacts created today' },
        { value: 'yesterday', label: 'Yesterday', description: 'Contacts created yesterday' },
        { value: 'this_week', label: 'This Week', description: 'Contacts created this week' },
        { value: 'last_week', label: 'Last Week', description: 'Contacts created last week' },
        { value: 'this_month', label: 'This Month', description: 'Contacts created this month' },
        { value: 'last_month', label: 'Last Month', description: 'Contacts created last month' },
        { value: 'this_year', label: 'This Year', description: 'Contacts created this year' },
        { value: 'last_year', label: 'Last Year', description: 'Contacts created last year' },
        { value: 'custom', label: 'Custom Range', description: 'Select custom date range' },
        { value: 'all', label: 'All Time', description: 'All contacts' }
      ],
      defaultSort: {
        field: 'createdAt',
        direction: 'desc'
      },
      searchableFields: ['name', 'email', 'phone', 'tags']
    }
  }

  // ✨ Build MongoDB queries from user parameters
  buildMongoQueries(userParams: Record<string, any>) {
    return buildMongoQueries(this.filters, userParams)
  }

  // ✨ Get filter by ID
  getFilterById(id: string): SearchFilter | null {
    return this.filters.find(filter => filter.getId() === id) || null
  }

  // ✨ Get all filter IDs
  getFilterIds(): string[] {
    return this.filters.map(filter => filter.getId())
  }

  // ✨ Validate user input
  validateInput(userParams: Record<string, any>): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    
    for (const filter of this.filters) {
      const filterId = filter.getId()
      const userValue = userParams[filterId]
      
      if (userValue !== undefined && userValue !== null) {
        try {
          const query = filter.build(userValue)
          if (query === null) {
            errors.push(`Invalid value for filter '${filterId}': ${userValue}`)
          }
        } catch (error) {
          errors.push(`Error processing filter '${filterId}': ${error instanceof Error ? error.message : 'Unknown error'}`)
        }
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }

  // ✨ Add custom filter
  addFilter(filter: SearchFilter): void {
    this.filters.push(filter)
  }

  // ✨ Remove filter by ID
  removeFilter(id: string): boolean {
    const index = this.filters.findIndex(filter => filter.getId() === id)
    if (index !== -1) {
      this.filters.splice(index, 1)
      return true
    }
    return false
  }

  // ✨ Replace filter
  replaceFilter(id: string, newFilter: SearchFilter): boolean {
    const index = this.filters.findIndex(filter => filter.getId() === id)
    if (index !== -1) {
      this.filters[index] = newFilter
      return true
    }
    return false
  }
}

// ✨ Export singleton instance
export const contactsSearchConfig = new ContactsSearchConfig()
