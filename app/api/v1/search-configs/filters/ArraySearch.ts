import { SearchFilter, MongoSearchQuery, ApiFilterResponse } from './base'

export interface ArraySearchOptions {
  matchAll?: boolean // Require all values to match (AND) vs any value (OR)
  caseSensitive?: boolean // Case sensitive matching
  exactMatch?: boolean // Exact match vs contains
  allowedValues?: string[] // Predefined list of allowed values
  minItems?: number // Minimum number of items required
  maxItems?: number // Maximum number of items allowed
}

// ✨ ArraySearch filter for array-based searches (tags, categories, etc.)
export class ArraySearch extends SearchFilter {
  private options: ArraySearchOptions

  constructor(
    id: string,
    field: string,
    label: string,
    options: ArraySearchOptions = {}
  ) {
    super(id, field, label)
    this.options = options
  }

  // ✨ Build MongoDB query for array search
  build(value: any): MongoSearchQuery | null {
    if (!this.isValidValue(value)) {
      return null
    }

    // Normalize input to array
    let searchValues: string[] = []
    
    if (typeof value === 'string') {
      // Split by comma and trim
      searchValues = value.split(',').map(v => v.trim()).filter(v => v.length > 0)
    } else if (Array.isArray(value)) {
      searchValues = value.filter(v => typeof v === 'string' && v.trim().length > 0)
        .map(v => v.trim())
    } else {
      return null
    }

    if (searchValues.length === 0) {
      return null
    }

    // Validate against allowed values if provided
    if (this.options.allowedValues) {
      const allowedSet = new Set(this.options.allowedValues.map(v => 
        this.options.caseSensitive ? v : v.toLowerCase()
      ))
      
      searchValues = searchValues.filter(v => 
        allowedSet.has(this.options.caseSensitive ? v : v.toLowerCase())
      )
      
      if (searchValues.length === 0) {
        return null
      }
    }

    // Validate item count constraints
    if (this.options.minItems && searchValues.length < this.options.minItems) {
      return null
    }

    if (this.options.maxItems && searchValues.length > this.options.maxItems) {
      return null
    }

    // Build MongoDB query
    if (this.options.exactMatch) {
      // Exact match in array
      if (this.options.matchAll) {
        // All values must be present in the array
        return {
          [this.field]: {
            $all: this.options.caseSensitive 
              ? searchValues 
              : searchValues.map(v => new RegExp(`^${this.escapeRegex(v)}$`, 'i'))
          }
        }
      } else {
        // Any value must be present in the array
        return {
          [this.field]: {
            $in: this.options.caseSensitive 
              ? searchValues 
              : searchValues.map(v => new RegExp(`^${this.escapeRegex(v)}$`, 'i'))
          }
        }
      }
    } else {
      // Contains match in array elements
      if (this.options.matchAll) {
        // All search values must have matching elements in the array
        return {
          $and: searchValues.map(searchValue => ({
            [this.field]: {
              $elemMatch: this.options.caseSensitive
                ? { $regex: this.escapeRegex(searchValue) }
                : { $regex: this.escapeRegex(searchValue), $options: 'i' }
            }
          }))
        }
      } else {
        // Any search value can have matching elements in the array
        return {
          [this.field]: {
            $elemMatch: {
              $or: searchValues.map(searchValue => 
                this.options.caseSensitive
                  ? { $regex: this.escapeRegex(searchValue) }
                  : { $regex: this.escapeRegex(searchValue), $options: 'i' }
              )
            }
          }
        }
      }
    }
  }

  // ✨ Build API response for frontend
  buildForApiResponse(): ApiFilterResponse {
    const response: ApiFilterResponse = {
      id: this.id,
      name: this.label,
      field: this.field,
      type: 'array'
    }

    // Add allowed values as options if provided
    if (this.options.allowedValues) {
      response.options = this.options.allowedValues.map(value => ({
        value,
        label: value
      }))
    }

    // Add validation rules
    const validation: any = {}
    
    if (this.options.minItems) {
      validation.minItems = this.options.minItems
    }
    
    if (this.options.maxItems) {
      validation.maxItems = this.options.maxItems
    }

    if (Object.keys(validation).length > 0) {
      response.validation = validation
    }

    // Add search behavior hints
    response.matchAll = this.options.matchAll || false
    response.caseSensitive = this.options.caseSensitive || false
    response.exactMatch = this.options.exactMatch || false

    return response
  }



  // ✨ Static factory methods for common use cases
  static tags(id: string, field: string, label: string, allowedTags?: string[]): ArraySearch {
    return new ArraySearch(id, field, label, {
      allowedValues: allowedTags,
      exactMatch: true,
      caseSensitive: false,
      matchAll: false
    })
  }

  static categories(id: string, field: string, label: string, allowedCategories?: string[]): ArraySearch {
    return new ArraySearch(id, field, label, {
      allowedValues: allowedCategories,
      exactMatch: true,
      caseSensitive: false,
      matchAll: false
    })
  }

  static keywords(id: string, field: string, label: string, options: ArraySearchOptions = {}): ArraySearch {
    return new ArraySearch(id, field, label, {
      exactMatch: false,
      caseSensitive: false,
      matchAll: false,
      ...options
    })
  }
}
